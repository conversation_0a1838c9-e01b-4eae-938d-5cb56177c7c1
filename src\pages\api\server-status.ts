import { NextApiRequest, NextApiResponse } from 'next';
import { getServerStatus, isServerRestarted, shouldClearPortfolios } from '../../lib/server-session';

/**
 * 서버 상태 확인 API
 * 클라이언트가 서버 재시작 여부를 확인할 수 있도록 함
 * Vercel 서버리스 환경에 최적화됨
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Vercel 환경에서 명시적인 HTTP 메서드 검증
  if (req.method !== 'GET') {
    return res.status(405).json({
      error: 'Method not allowed',
      message: 'Only GET method is supported for this endpoint'
    });
  }

  try {
    const { lastKnownStartTime } = req.query;
    const clientLastKnownStartTime = lastKnownStartTime ? parseInt(lastKnownStartTime as string) : undefined;

    // Vercel 서버리스 환경에서 안전한 상태 확인
    const serverStatus = getServerStatus();
    const restarted = isServerRestarted(clientLastKnownStartTime);

    res.status(200).json({
      ...serverStatus,
      restarted,
      shouldClearPortfolios: shouldClearPortfolios(), // 서버 시작 후 첫 번째 요청에서만 true
      environment: 'vercel-serverless' // 환경 정보 추가
    });
  } catch (error) {
    console.error('❌ [Server Status] API 오류:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: '서버 상태를 확인할 수 없습니다.'
    });
  }
}
