/**
 * Vercel API 엔드포인트 테스트 스크립트
 * 
 * 모든 API 라우트가 올바른 HTTP 메서드로 응답하는지 확인합니다.
 * 사용법: node scripts/test-vercel-apis.js [BASE_URL]
 * 
 * 예시:
 * node scripts/test-vercel-apis.js https://your-app.vercel.app
 * node scripts/test-vercel-apis.js http://localhost:3000
 */

const BASE_URL = process.argv[2] || 'http://localhost:3000';

console.log(`🚀 Vercel API 엔드포인트 테스트 시작`);
console.log(`📍 Base URL: ${BASE_URL}`);
console.log('');

// 테스트할 API 엔드포인트 목록
const API_TESTS = [
  {
    name: 'Server Status API',
    endpoint: '/api/server-status',
    method: 'GET',
    expectedStatus: 200
  },
  {
    name: 'AI Chat API (잘못된 메서드)',
    endpoint: '/api/ai_chat',
    method: 'GET',
    expectedStatus: 405
  },
  {
    name: 'AI Chat API (올바른 메서드)',
    endpoint: '/api/ai_chat',
    method: 'POST',
    body: { message: '안녕하세요' },
    expectedStatus: [200, 400, 500] // 환경변수에 따라 다를 수 있음
  },
  {
    name: 'Simple AI Chat API (잘못된 메서드)',
    endpoint: '/api/simple_ai_chat',
    method: 'GET',
    expectedStatus: 405
  },
  {
    name: 'Market Data API (잘못된 메서드)',
    endpoint: '/api/market_data',
    method: 'POST',
    expectedStatus: 405
  },
  {
    name: 'Market Data API (올바른 메서드)',
    endpoint: '/api/market_data?symbol=AAPL',
    method: 'GET',
    expectedStatus: [200, 400, 500] // 외부 API에 따라 다를 수 있음
  },
  {
    name: 'SpeedTraffic Analysis API (잘못된 메서드)',
    endpoint: '/api/speedtraffic_analysis',
    method: 'POST',
    expectedStatus: 405
  },
  {
    name: 'SpeedTraffic Commentary API (잘못된 메서드)',
    endpoint: '/api/speedtraffic_commentary',
    method: 'GET',
    expectedStatus: 405
  },
  {
    name: 'Generate Report API (잘못된 메서드)',
    endpoint: '/api/generate_report',
    method: 'GET',
    expectedStatus: 405
  },
  {
    name: 'Backtest API (잘못된 메서드)',
    endpoint: '/api/backtest',
    method: 'GET',
    expectedStatus: 405
  }
];

// 테스트 실행 함수
async function runTest(test) {
  try {
    const options = {
      method: test.method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (test.body) {
      options.body = JSON.stringify(test.body);
    }

    const response = await fetch(`${BASE_URL}${test.endpoint}`, options);
    const status = response.status;
    
    // 예상 상태 코드 확인
    const expectedStatuses = Array.isArray(test.expectedStatus) 
      ? test.expectedStatus 
      : [test.expectedStatus];
    
    const isExpected = expectedStatuses.includes(status);
    const statusIcon = isExpected ? '✅' : '❌';
    
    console.log(`${statusIcon} ${test.name}`);
    console.log(`   ${test.method} ${test.endpoint}`);
    console.log(`   응답: ${status} (예상: ${expectedStatuses.join(' 또는 ')})`);
    
    if (!isExpected) {
      const responseText = await response.text();
      console.log(`   오류 내용: ${responseText.substring(0, 200)}...`);
    }
    
    console.log('');
    return isExpected;
    
  } catch (error) {
    console.log(`❌ ${test.name}`);
    console.log(`   ${test.method} ${test.endpoint}`);
    console.log(`   오류: ${error.message}`);
    console.log('');
    return false;
  }
}

// 모든 테스트 실행
async function runAllTests() {
  console.log('📋 API 엔드포인트 테스트 실행 중...\n');
  
  let passedTests = 0;
  let totalTests = API_TESTS.length;
  
  for (const test of API_TESTS) {
    const passed = await runTest(test);
    if (passed) passedTests++;
    
    // 테스트 간 간격
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('📊 테스트 결과 요약');
  console.log(`✅ 통과: ${passedTests}/${totalTests}`);
  console.log(`❌ 실패: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 모든 API 엔드포인트가 올바르게 작동합니다!');
  } else {
    console.log('\n⚠️  일부 API 엔드포인트에 문제가 있습니다. 위의 오류를 확인해주세요.');
  }
}

// 스크립트 실행
runAllTests().catch(error => {
  console.error('❌ 테스트 실행 중 오류 발생:', error);
  process.exit(1);
});
