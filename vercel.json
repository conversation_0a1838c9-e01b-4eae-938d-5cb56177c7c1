{"buildCommand": "npm run build", "installCommand": "npm ci", "outputDirectory": ".next", "framework": "nextjs", "functions": {"src/pages/api/**/*.ts": {"maxDuration": 30, "memory": 1024, "runtime": "nodejs18.x"}, "api/**/*.py": {"maxDuration": 30, "memory": 1024}}, "regions": ["icn1"], "env": {"SKIP_ENV_VALIDATION": "true"}, "build": {"env": {"PYTHON_VERSION": "3.9", "NODE_VERSION": "18"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}